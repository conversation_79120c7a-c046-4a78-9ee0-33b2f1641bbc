<%
  # 获取终端链接
  terminal_link = ''
  if defined?(@pinned_apps) && @pinned_apps
    @pinned_apps.each do |app|
      link = app.links.first
      if link && link.icon_uri.to_s == 'fas://terminal'
        terminal_link = link.url.to_s
        break
      end
    end
  end
%>

<div class="right3">
  <div style="color:#666666;">常用工具</div>
  <div style="width: 100%;height: 72px;border-radius: 16px;background: #5B93FF0D;display:flex;justify-content: space-around;">
    <div style="display: flex;align-items:center;gap:8px;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>
        <div>脚本任务</div>
        <div style="font-size: 12px;color:#666666;">运行.sh文件</div>
      </div>
    </div>
    <div style="display: flex;align-items:center;gap:8px;cursor:pointer;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>
        <div>桌面环境</div>
        <div style="font-size: 12px;color:#666666;">启动CentOS</div>
      </div>
    </div>
    <div style="display: flex;align-items:center;gap:8px;cursor:pointer;" onclick="redirectToFiles()">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>
        <div>文件管理</div>
        <div style="font-size: 12px;color:#666666;">查看文件目录</div>
      </div>
    </div>
    <div id="terminal-btn" style="display: flex;align-items:center;gap:8px;cursor:pointer;" onclick="showTerminalModal()">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>
        <div>启动终端</div>
        <div style="font-size: 12px;color:#666666;">shell命令</div>
      </div>
    </div>
  </div>
  <div style="margin-top: 24px;color:#666666;">常用任务</div>
  <div style="display: flex;gap: 90px;margin-top: 8px;padding-left:25px">
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
  </div>
  <div style="margin-top: 24px;color:#666666;">脚本任务</div>
  <div style="display: flex;gap: 90px;margin-top: 8px;padding-left:25px">
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>脚本训练</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>脚本训练2</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>AI训练</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
  </div>
  <div style="margin-top: 24px;color:#666666;">实例任务</div>
  <div style="display: flex;column-gap: 90px;row-gap: 24px;margin-top: 8px;padding-left:20px;flex-wrap: wrap;">
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>脚本训练</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>脚本训练2</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>AI训练</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
    <div style="text-align: center;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
      <div>自制模板1</div>
    </div>
  </div>

  <!-- 终端弹窗 -->
  <div id="terminal-modal" style="display:none; position: fixed; z-index: 9999; left: 0; top: 0; width: 100vw; height: 100vh; background: rgba(0,0,0,0.3);">
    <div style="background: #fff; border-radius: 16px; width: 90%; max-width: 1200px; height: 80%; margin: 5% auto; position: relative; box-shadow: 0 2px 16px rgba(0,0,0,0.15); display: flex; flex-direction: column;">
      <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 1px solid #eee;">
        <div style="font-size: 20px; font-weight: 600;">终端</div>
        <button type="button" id="close-terminal-modal" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">&times;</button>
      </div>
      <div style="flex: 1; padding: 0;">
        <iframe id="terminal-iframe" src="" frameborder="0" style="width: 100%; height: 100%; border: none;"></iframe>
      </div>
    </div>
  </div>

  <script>
    // 显示终端弹窗
    function showTerminalModal() {
      const modal = document.getElementById('terminal-modal');
      const iframe = document.getElementById('terminal-iframe');
      const terminalUrl = "<%= terminal_link %>";

      if (terminalUrl && terminalUrl.trim() !== '') {
        iframe.src = terminalUrl;
        modal.style.display = 'block';
      } else {
        alert('终端服务不可用，请检查配置');
      }
    }

    // 关闭终端弹窗
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('close-terminal-modal').onclick = function() {
        const modal = document.getElementById('terminal-modal');
        const iframe = document.getElementById('terminal-iframe');
        modal.style.display = 'none';
        iframe.src = ''; // 清空iframe源以停止终端连接
      };

      // 点击弹窗背景关闭
      document.getElementById('terminal-modal').onclick = function(e) {
        if (e.target === this) {
          const iframe = document.getElementById('terminal-iframe');
          this.style.display = 'none';
          iframe.src = ''; // 清空iframe源以停止终端连接
        }
      };
    });

    function redirectToFiles() {
      window.location.href = "<%= files_path(Dir.home) %>";
    }
  </script>
</div>