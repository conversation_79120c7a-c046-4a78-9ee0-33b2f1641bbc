<!DOCTYPE html>
<html>

<head>
  <title><%= content_for?(:title) ? yield(:title) : "超算应用平台|华信鼎成" %></title>
  <%= favicon_link_tag 'favicon.ico', href: OodAppkit.public.url.join('favicon.ico'), skip_pipeline: true %>

  <%= csrf_meta_tags %>

  <%= yield :head %>

  <meta name="viewport" content="width=device-width, initial-scale=1">
  <% if Configuration.turbolinks_enabled? %>
    <meta name="turbolinks-root" content="<%= ENV['RAILS_RELATIVE_URL_ROOT'] || "/" %>">
  <% end %>
  <%= render partial: '/layouts/nav/styles', locals: { bg_color: Configuration.brand_bg_color, link_active_color: Configuration.brand_link_active_bg_color } %>
  <%= javascript_pack_tag 'application' %>
  <%= stylesheet_pack_tag 'application', media: 'all' %>

  <!-- (Legacy) Sprockets -->
  <%= stylesheet_link_tag 'application', media: 'all' %>
  <%= javascript_include_tag 'application' %>
  <%= javascript_include_tag 'turbolinks' if Configuration.turbolinks_enabled? %>
  <style>
    body {
      background-image: url('<%= image_path "/new/home/<USER>" %>');
      background-repeat: no-repeat;
      background-size: cover;      /* 让背景图铺满整个页面 */
      background-position: center; /* 居中显示 */
    }

    .input-placeholder-light {
      border-color:#e5e5e5;
      width: 100%;
      font-size: 14px;
      padding: 23px 16px;
      background:#f9f9f9;
      ::placeholder{color:#bfbfbf;opacity:1;}
    }

    .submit-button {
      font-size: 14px;
      padding: 12px;
      background: #007Aff;
    }
  .input-placeholder-light::placeholder {
    color: #bfbfbf !important;
    opacity: 1;
  }
  .input-placeholder-light::-webkit-input-placeholder {
    color: #bfbfbf !important;
    opacity: 1;
  }
  .input-placeholder-light::-moz-placeholder {
    color: #bfbfbf !important;
    opacity: 1;
  }
  .input-placeholder-light:-ms-input-placeholder {
    color: #bfbfbf !important;
    opacity: 1;
  }
  .input-placeholder-light::-ms-input-placeholder {
    color: #bfbfbf !important;
    opacity: 1;
  }

  input.form-control {
    border-radius: 6px;
  }

  input.form-control:focus,
  textarea.form-control:focus {
    border-color: #007Aff !important;
    border-width: 1px;
    box-shadow: none;
    outline: none;
  }
  </style>
  <style>
    .dock {
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(255, 255, 255, 0);
      padding: 10px 20px;
      border-radius: 16px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.2);
      display: flex;
      gap: 24px;
      z-index: 999;
      backdrop-filter: blur(20px);
    }

    .dock-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 12px;
      color: #333;
    }

    .dock-item img {
      width: 48px;
      height: 48px;
      transition: transform 0.3s ease;
    }

    .dock-item span {
      margin-top: 6px;
    }

    /* 分割线样式，仅用于第一个图标后 */
    .with-divider {
      position: relative;
      padding-right: 24px;
      margin-right: 24px;
    }

    .with-divider::after {
      content: "";
      position: absolute;
      top: 10%;
      right: 0;
      width: 1px;
      height: 80%;
      background-color: #ccc;
    }
    .left1 {
      width: 300px;
      height: 380px;
      border-radius: 16px;
      background: white;
      display: flex;
      justify-content: center;
    }
    .left2 {
      width: 300px;
      height: 344px;
      border-radius: 16px;
      background: white;
      margin-top: 20px;
      padding-left: 26px;
      padding-right: 26px;
      padding-top: 1px;
    }

    .right1 {
      width: 960px;height: 208px;border-radius: 16px;background: white;
    }

    .right2 {
      display: flex;gap: 12px;margin-top: 20px;
    }

    .right3 {
      width: 960px;border-radius: 16px;background: white;margin-top: 20px;
      padding-left: 50px;
      padding-right: 60px;
      padding-top: 24px;
    }

    .avatar {
      height: 64px;
      width: 64px;
      border-radius: 50%;
    }
    .circle-row {
      display: flex;
      justify-content: center;
      align-items: flex-end;
      margin-bottom: 24px;
      gap: 40px;
      padding-top: 30px;
    }
    .circle-col {
      display: flex;
      flex-direction: column;
    }
  </style>
</head>

<body>
  <% own_valid_license = Cipher.get_license_list&.pluck(:status)&.include?("激活") %>
  <% if @user.name != 'admin' && !own_valid_license %>
    <div style="width:100%; position:relative;z-index:1;margin:0 auto; background: #000;">
      <div style="margin-top: 80px;width:100%;position:absolute;z-index:2;text-align:center;background:#0000005e;height: calc(100vh - 80px);">
        <h2 style="color:#000000;padding-top: 200px;">请导入 License 许可！</h2>
      </div>
    </div>
  <% end %>
  <%= yield :content %>
</body>

</html>
