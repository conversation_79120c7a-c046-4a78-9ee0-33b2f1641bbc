<div>
  <div style="margin-top: 24px;width: 248px;text-align: left;color:#666666;">
    我的模板
  </div>
  <div id="my-templates-list" style="display: flex; flex-wrap: wrap; gap: 14px;margin-top: 8px;">
    <!-- 新建模板按钮始终显示 -->
    <div id="new-template-btn" style="width: calc(33.333% - 10.33px); text-align: center; cursor: pointer;">
      <div><%= image_tag "/new/home/<USER>", style: "width: 40px;" %></div>
      <div>新建模板</div>
    </div>
    <!-- 其余模板通过 JS 动态插入 -->
  </div>

  <!-- 新建模板弹窗 -->
  <div id="new-template-modal" style="display:none; position: fixed; z-index: 9999; left: 0; top: 0; width: 100vw; height: 100vh; background: rgba(0,0,0,0.3);">
    <div style="background: #fff; border-radius: 16px; width: 560px; margin: 100px auto; padding: 40px; position: relative; box-shadow: 0 2px 16px rgba(0,0,0,0.15);">
      <div style="font-size: 20px; font-weight: 600; margin-bottom: 18px;">新建模板</div>
      <div id="new-template-form" autocomplete="off">
        <div style="margin-bottom: 14px;">
          <label for="template-name" style="display:block; margin-bottom: 4px;">模板名称</label>
          <input id="template-name" name="name" type="text" class="form-control input-placeholder-light" required>
        </div>
        <div style="margin-bottom: 14px;">
          <label for="template-script" style="display:block; margin-bottom: 4px;">脚本路径（将通过从此源路径复制文件来创建模板）</label>
          <input id="template-script" name="script_path" type="text" class="form-control input-placeholder-light">
        </div>
        <div style="margin-bottom: 14px;">
          <label for="template-cluster" style="display:block; margin-bottom: 4px;">集群</label>
          <select id="template-cluster" name="cluster" class="form-select input-placeholder-light" style="padding: 15px 16px;">
            <option value="">请选择集群</option>
            <option value="cluster">Cluster</option>
            <!-- 可根据实际集群动态生成 -->
          </select>
        </div>
        <div style="margin-bottom: 18px;">
          <label for="template-desc" style="display:block; margin-bottom: 4px;">模板备注</label>
          <textarea id="template-desc" name="desc" rows="2" class="form-control input-placeholder-light"></textarea>
        </div>
        <div style="display: flex; justify-content: flex-end; gap: 12px;">
          <button type="button" class="btn btn-primary btn-block submit-button" id="save-template">保存模板</button>
        </div>
      </div>
      <span id="close-template-modal" style="position: absolute; right: 16px; top: 12px; font-size: 22px; color: #888; cursor: pointer;">&times;</span>
    </div>
  </div>

  <!-- 查看模板详情弹窗 -->
  <div id="view-template-modal" style="display:none; position: fixed; z-index: 9999; left: 0; top: 0; width: 100vw; height: 100vh; background: rgba(0,0,0,0.3);">
    <div style="background: #fff; border-radius: 16px; width: 560px; margin: 100px auto; padding: 40px; position: relative; box-shadow: 0 2px 16px rgba(0,0,0,0.15);">
      <div style="font-size: 20px; font-weight: 600; margin-bottom: 18px;">模板详情</div>
      <div id="view-template-content">
        <!-- 详情内容通过JS填充 -->
      </div>
      <div id="view-template-actions" style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 12px;">
        <button type="button" class="btn btn-primary submit-button" id="add-to-queue-btn" style="flex: 1;">加入队列</button>
        <button type="button" class="btn btn-danger" id="delete-template-btn" style="flex: 1;">删除模板</button>
      </div>
      <span id="close-view-template-modal" style="position: absolute; right: 16px; top: 12px; font-size: 22px; color: #888; cursor: pointer;">&times;</span>
    </div>
  </div>
</div>

<script>
  // 用于缓存模板数据，便于详情弹窗使用
  let templateListCache = [];
  // 当前详情弹窗展示的模板索引
  let currentTemplateIdx = null;

  document.addEventListener("DOMContentLoaded", function() {
    fetch("https://*************/pun/sys/myjobs/templates", {
      method: "GET",
      credentials: "include"
    })
      .then(res => res.json())
      .then(data => {
        if (data && Array.isArray(data.list)) {
          templateListCache = data.list; // 缓存
          const container = document.getElementById("my-templates-list");
          data.list.forEach(function(item, idx) {
            // 解析模板名
            const name = (item.manifest && item.manifest.name) ? item.manifest.name : "自制模板";
            const path = item.path || "#";
            const img = "/new/home/<USER>";
            // 创建元素
            const div = document.createElement("div");
            div.style.width = "calc(33.333% - 10.33px)";
            div.style.textAlign = "center";
            div.style.cursor = "pointer";
            div.setAttribute("data-template-idx", idx);
            div.innerHTML = `
              <div><%= image_tag "/new/home/<USER>", style: "width: 40px;" %></div>
              <div data-path="${path}">${name}</div>
            `;
            // 点击弹窗显示详情
            div.onclick = function(e) {
              showTemplateDetailModal(idx);
            };
            container.appendChild(div);
          });
        }
      })
      .catch(err => {
        // 可选：显示错误提示
        console.error("模板加载失败", err);
      });
  });

  // 显示模板详情弹窗
  function showTemplateDetailModal(idx) {
    const modal = document.getElementById('view-template-modal');
    const content = document.getElementById('view-template-content');
    const item = templateListCache[idx];
    if (!item) return;
    // 解析字段
    const name = (item.manifest && item.manifest.name) ? item.manifest.name : "自制模板";
    const script = item.path || "";
    const cluster = item.host || (item.manifest && item.manifest.host) || "";
    const desc = (item.manifest && item.manifest.notes) || item.notes || "";
    // 填充内容（只读）
    content.innerHTML = `
      <div style="margin-bottom: 14px;">
        <label style="display:block; margin-bottom: 4px;">模板名称</label>
        <input type="text" class="form-control input-placeholder-light" value="${name}" readonly>
      </div>
      <div style="margin-bottom: 14px;">
        <label style="display:block; margin-bottom: 4px;">脚本路径</label>
        <input type="text" class="form-control input-placeholder-light" value="${script}" readonly>
      </div>
      <div style="margin-bottom: 14px;">
        <label style="display:block; margin-bottom: 4px;">集群</label>
        <input type="text" class="form-control input-placeholder-light" value="${cluster}" readonly>
      </div>
      <div style="margin-bottom: 18px;">
        <label style="display:block; margin-bottom: 4px;">模板备注</label>
        <textarea rows="2" class="form-control input-placeholder-light" readonly>${desc}</textarea>
      </div>
    `;
    modal.style.display = 'block';
    currentTemplateIdx = idx;
  }

  // 新建模板弹窗相关
  document.getElementById('new-template-btn').onclick = function() {
    document.getElementById('new-template-modal').style.display = 'block';
  };
  document.getElementById('close-template-modal').onclick = function() {
    document.getElementById('new-template-modal').style.display = 'none';
  };
  document.getElementById('new-template-modal').onclick = function(e) {
    if (e.target === this) this.style.display = 'none';
  };

  // 查看模板详情弹窗相关
  document.getElementById('close-view-template-modal').onclick = function() {
    document.getElementById('view-template-modal').style.display = 'none';
  };
  document.getElementById('view-template-modal').onclick = function(e) {
    if (e.target === this) this.style.display = 'none';
  };

  // 加入队列按钮事件
  document.getElementById('add-to-queue-btn').onclick = function() {
    if (currentTemplateIdx === null || !templateListCache[currentTemplateIdx]) {
      alert('未找到模板信息');
      return;
    }
    const item = templateListCache[currentTemplateIdx];

    // 构建workflow参数，根据您提供的接口规范
    const workflowData = {
      workflow: {
        name: (item.manifest && item.manifest.name) ? item.manifest.name : "AI训练模板",
        batch_host: item.host || (item.manifest && item.manifest.host) || "cluster",
        script_name: "main_job.job",
        staging_template_dir: "/home/<USER>/cloud/data/sys/myjobs/templates/ai"
      },
      commit: "创建新作业"
    };

    // 调用加入队列API
    fetch('https://*************/pun/sys/myjobs/workflows', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(workflowData)
    })
    // .then(res => res.json())
    .then(data => {
      //if (data && (data.success || data.status === 'ok')) {
        alert('已成功加入队列');
        document.getElementById('view-template-modal').style.display = 'none';
      //} else {
      //  alert(data.message || '加入队列失败');
      //}
    })
    .catch(err => {
      alert('请求失败: ' + err);
    });
  };

  // 删除模板按钮事件
  document.getElementById('delete-template-btn').onclick = function() {
    if (currentTemplateIdx === null || !templateListCache[currentTemplateIdx]) {
      alert('未找到模板信息');
      return;
    }
    if (!confirm('确定要删除该模板吗？')) return;
    const item = templateListCache[currentTemplateIdx];

    // 获取模板路径
    const templatePath = item.path;
    if (!templatePath) {
      alert('模板路径缺失，无法删除');
      return;
    }

    // 调用删除模板API，使用path参数
    const deleteUrl = `https://*************/pun/sys/myjobs/templates/delete?path=${templatePath}`;

    fetch(deleteUrl, {
      method: 'DELETE',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    // .then(res => res.json())
    .then(data => {
     // if (data && (data.success || data.status === 'ok')) {
        alert('模板已删除');
        window.location.reload();
      //} else {
      //  alert(data.message || '删除失败');
      //}
    })
    .catch(err => {
      alert('请求失败: ' + err);
    });
  };

  // 表单提交
  document.getElementById('save-template').onclick = function(e) {
    e.preventDefault();
    // 获取表单数据
    var name = document.getElementById('template-name').value.trim();
    var script = document.getElementById('template-script').value.trim();
    var cluster = document.getElementById('template-cluster').value;
    var desc = document.getElementById('template-desc').value.trim();
    if (!name) {
      alert('请填写完整信息');
      return false;
    }
    // 这里可以用AJAX提交到后端，或直接在前端插入新模板
    // 使用AJAX提交到后端
    fetch('https://*************/pun/sys/myjobs/templates.json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: name,
        path: script,
        host: cluster,
        notes: desc
      })
    })
    // .then(response => response.json())
    .then(data => {
      //if (data.success) {
        // 可根据需要处理成功逻辑
        //alert('模板保存成功');
      window.location.reload();
      //} else {
      //  alert(data.message || '模板保存失败');
      //}
    })
    .catch(error => {
      alert('请求失败: ' + error);
    });
    // 示例：前端插入
    var list = document.getElementById('my-templates-list');
    var div = document.createElement('div');
    div.style.width = "calc(33.333% - 10.33px)";
    div.style.textAlign = "center";
    div.style.cursor = "pointer";
    div.style.background = "#f7f7f7";
    div.style.borderRadius = "6px";
    div.style.padding = "10px 0 8px 0";
    div.style.boxShadow = "0 1px 4px rgba(0,0,0,0.04)";
    div.innerHTML = `
      <div><%= image_tag "/new/home/<USER>", style: "width: 40px;" %></div>
      <div style="font-weight:600;">${name}</div>
    `;
    // 可添加点击事件跳转或编辑
    list.appendChild(div);
    document.getElementById('new-template-modal').style.display = 'none';
    this.reset && this.reset();
  };
</script>
