<%# z-index:999 cause dropdowns are 1000 default sticky-top is 1020 https://getbootstrap.com/docs/4.6/layout/overview/#z-index %>
<div class="text-right sticky-top bg-white border-bottom p-2 m-3" style="z-index: 999">
  <!-- terminal button href is set via JavaScript because the URL is based on the current directory -->
  <% if Configuration.files_enable_shell_button %>
    <%= render partial: 'shell_dropdown' %>
  <% end %>

  <button id="new-file-btn" type="button" class="btn btn-outline-dark btn-sm"><i class="fas fa-plus" aria-hidden="true"></i> 创建文件</button>
  <button id="new-dir-btn" type="button" class="btn btn-outline-dark btn-sm"><i class="fas fa-folder-plus" aria-hidden="true"></i> 创建目录</button>
  <button id="upload-btn" type="button" class="btn btn-primary btn-sm"><i class="fas fa-upload" aria-hidden="true"></i> 上传</button>
  <button id="download-btn" type="button" class="btn btn-primary btn-sm"><i class="fas fa-download" aria-hidden="true"></i> 下载</button>
  <button id="copy-move-btn" type="button" class="btn btn-outline-dark btn-sm"><i class="fas fa-copy" aria-hidden="true"></i> 拷贝/剪切</button>
  <button id="delete-btn" type="button" class="btn btn-danger btn-sm"><i class="fas fa-trash" aria-hidden="true"></i> 删除</button>
</div>

<!-- <hr> -->

<div class="row">
<div class="col-md-12">
  <div id="clipboard" class="sticky-top"></div>

  <!-- <nav>
    <ul id="favorites" class="nav nav-pills flex-column">
      <li role="presentation" class="nav-item"><%= link_to '用户 目录', files_path(Dir.home), class: "nav-link d bg-light" %></li>
      <% OodFilesApp.new.favorite_paths.each do |p| %>
        <li class="nav-item"><%= link_to p.title || p.path.to_s, files_path(p.path.to_s), class: "nav-link d bg-light" %>
      <% end %>
    </ul>
  </nav> -->

<script id="clipboard-template" type="text/template">
{{#if files}}
<div class="card mb-3">
  <div class="card-body">
    <button id="clipboard-clear" type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <p style="margin-top: 30px">Copy or move the files below from <code>{{from}}</code> to the current directory:</p>
  </div>
  <ul class="list-group list-group-flush">
    {{#each files}}
      {{#if directory}}
        <li class="list-group-item"><span title="directory" class="fa fa-folder" style="color: gold"></span> {{name}}</li>
      {{else}}
        <li class="list-group-item"><span title="file" class="fa fa-file" style="color: lightgrey"></span> {{name}}</li>
      {{/if}}
    {{/each}}
  </ul>
  <div class="card-body">
    <button id="clipboard-copy-to-dir" class="btn btn-primary">Copy</button>  <button id="clipboard-move-to-dir" class="btn btn-danger">Move</button>
  </div>
</div>
{{/if}}
</script>


<script id="actions-btn-template" type="text/template">
<div class="btn-group actions-btn-group">
  <%# FIXME: outline dark is easy to see unless the row is selected, then it is difficult to see; consider a different row selection color or indicator
  for example the border-left: 4px solid #0088cc on the checkbox td
  and then some very very light blue highlight that still has the necessary contrast ratio with black on white/black on grey for WCAG
   %>
  <button type="button" class="actions-btn btn btn-outline-dark btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
    <span class="fa fa-ellipsis-v"></span>
  </button>
  <ul class="dropdown-menu">
    {{#if file}}
    <li><a href="{{data.url}}" class="view-file dropdown-item" target="_blank" data-row-index="{{row_index}}"><i class="fas fa-eye" aria-hidden="true"></i> View</a></li>
    <li><a href="{{data.edit_url}}" class="edit-file dropdown-item" target="_blank" data-row-index="{{row_index}}"><i class="fas fa-edit" aria-hidden="true"></i> Edit</a></li>
    {{/if}}
    <li><a href="#" class="rename-file dropdown-item" data-row-index="{{row_index}}"><i class="fas fa-font" aria-hidden="true"></i> 重命名</a></li>
    <li><a href="{{data.download_url}}" class="download-file dropdown-item" data-row-index="{{row_index}}"><i class="fas fa-download" aria-hidden="true"></i> 下载</a></li>
    <li class="dropdown-divider mt-4"></li>
    <li><a href="#" class="delete-file dropdown-item text-danger" data-row-index="{{row_index}}"><i class="fas fa-trash" aria-hidden="true"></i> 删除</a></li>
  </ul>
</div>
</script>

<script id="transfer-template" type="text/template">
{{#*inline "defaultLabel"}}
  <span id="{{id}}" class="text-{{bootstrap_class}}"><b><i class="fas {{fa_label}} %>"></i> {{summary}}</b></span>
{{/inline}}

{{#if completed}}
  {{#if error_summary}}
    <div id="{{id}}" class="alert alert-danger alert-dismissible fade show" role="alert">
      <b><i class="fas fa-exclamation-triangle"></i> {{error_summary}}</b>
      <button class="btn btn-outline-dark btn-sm ml-3 collapsed" type="button" data-toggle="collapse" data-target="#{{id}}-error-report" aria-expanded="false" aria-controls="#{{id}}-error-report">See details</button>
      <div id="{{id}}-error-report" class="collapse">
        <div class="mt-3 card">
          <pre class="card-body">{{error_message}}</pre>
        </div>
      </div>
      <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    </div>
  {{else}}
    {{> defaultLabel}}
  {{/if}}
{{else}}
  {{> defaultLabel}}
{{/if}}
</script>

</div>

<div class="col-md-12">
  <%# TODO put search box ABOVE the breadcrumbs %>
  <div style="margin-top: 20px">

    <ol id="path-breadcrumbs" class="breadcrumb breadcrumb-no-delimiter">
      <%= render partial: 'breadcrumb', collection: @path.descend, as: :file, locals: { file_count: @path.descend.count, full_path: @path } %>
    </ol>

    <table class="table table-striped table-condensed" id="directory-contents" style="width:100%">
      <thead>
        <tr>
          <th><span class="sr-only">Select</span></th>
          <th>类型</th>
          <th>名称</th>
          <th><span class="sr-only">Actions</span></th>
          <th>容量</th>
          <th>创建时间</th>
          <th>用户</th>
          <th>权限</th>
        </tr>
      </thead>
      <tbody>
      </tbody>
    </table>
  </div>
</div>

</div>

<script>
const csrf_token = document.querySelector('meta[name="csrf-token"]').content;

// this uses event delegation so it captures events even if dom element is recreated
var clipboardjs = new ClipboardJS('#copy-path');
clipboardjs.on('success', function(e) {
  $(e.trigger).tooltip({title: 'Copied path to clipboard!', trigger: 'manual', placement: 'bottom'}).tooltip('show');
  setTimeout(() => $(e.trigger).tooltip('hide'), 2000);
  e.clearSelection();
});
clipboardjs.on('error', function(e) {
  e.clearSelection();
});

history.replaceState({
  currentDirectory: '<%= @path %>',
  currentDirectoryUrl: '<%= files_path(@path) %>',
  currentDirectoryUpdatedAt: '<%= Time.now.to_i %>'
}, null);

$.fn.dataTable.ext.search.push(
    function( settings, data, dataIndex  ) {
      return getShowDotFiles() || ! data[2].startsWith('.');
    }
)

let actionsBtnTemplate = (function(){
  let template_str  = $('#actions-btn-template').html();
  return Handlebars.compile(template_str);
})();

function update_datatables_status(api){
  // from "function info ( api )" of https://cdn.datatables.net/select/1.3.1/js/dataTables.select.js
  let rows    = api.rows( { selected: true } ).flatten().length,
      page_info = api.page.info(),
      msg = page_info.recordsTotal == page_info.recordsDisplay ? `Showing ${page_info.recordsDisplay} rows` : `显示 ${page_info.recordsDisplay} 到 ${page_info.recordsTotal} 行`;

  $('.datatables-status').html(`${msg} - ${rows} 行`);
}

function getShowOwnerMode() {
  return localStorage.getItem('show-owner-mode') == 'true'
}

function getShowDotFiles() {
  return localStorage.getItem('show-dotfiles') == 'true'
}

function setShowOwnerMode(visible) {
  localStorage.setItem('show-owner-mode', new Boolean(visible));
}

function setShowDotFiles(visible) {
  localStorage.setItem('show-dotfiles', new Boolean(visible));
}

function updateDotFileVisibility() {
  table.draw();
}

function updateShowOwnerModeVisibility() {
  let visible = getShowOwnerMode();

  table.column('owner:name').visible(visible);
  table.column('mode:name').visible(visible);
}

var table = $('#directory-contents').on('xhr.dt', function ( e, settings, json, xhr ) {
  // new ajax request for new data so update date/time
  if(json && json.time){
    history.replaceState(_.merge({}, history.state, {currentDirectoryUpdatedAt: json.time}), null);
  }
}).DataTable({
  autoWidth: false,
  language: {
    search: '搜索:',
  },
  order: [[1, "asc"], [2, "asc"]],
  rowId: 'id',
  paging:false,
  scrollCollapse: true,
  select: {
    style: 'os',
    className: 'selected',
    toggleable: true,
    // don't trigger select checkbox column as select
    // if you need to omit more columns, use a "selectable" class on the columns you want to support selection
    selector: 'td:not(:first-child)'
  },
  // https://datatables.net/reference/option/dom
  // dom: '', dataTables_info nowrap
  //
  // put breadcrmbs below filter!!!
  dom: "<'row'<'col-sm-12'f>>" + // normally <'row'<'col-sm-6'l><'col-sm-6'f>> but we disabled pagination so l is not needed (dropdown for selecting # rows)
       "<'row'<'col-sm-12'<'dt-status-bar'<'datatables-status float-right'><'transfers-status'>>>>"+
       "<'row'<'col-sm-12'tr>>", // normally this is <'row'<'col-sm-5'i><'col-sm-7'p>> but we disabled pagination so have info take whole row
  columns: [
    {
      data: null,
      orderable: false,
      defaultContent: '<input type="checkbox">',
      render: function(data, type, row, meta) {
        var api = new $.fn.dataTable.Api( meta.settings );
        let selected = api.rows(meta.row, { selected: true }).count() > 0;
        return `<input type="checkbox" ${selected ? 'checked' : ''}> ${selected ? 'checked' : ''}`;
      }
    },
    { data: 'type', render: (data, type, row, meta) => data == 'd' ? '<span title="directory" class="fa fa-folder" style="color: gold"><span class="sr-only"> dir</span></span>' : '<span title="file" class="fa fa-file" style="color: lightgrey"><span class="sr-only"> file</span></span>' }, // type
    { name: 'name', data: 'name', className: 'text-break', render: (data, type, row, meta) => `<a class="${row.type} name ${row.type == 'd' ? '' : 'view-file' }" href="${row.url}">${Handlebars.escapeExpression(data)}</a>` }, // name
    { name: 'actions', orderable: false, data: null, render: (data, type, row, meta) => actionsBtnTemplate({ row_index: meta.row, file: row.type != 'd', data: row  }) }, // FIXME: pass row index or something needed for finding item
    { data: 'size',
      render: (data, type, row, meta) => {
        return type == "display" ? row.human_size : data;
      }
    }, // human_size
    { data: 'modified_at', render: (data, type, row, meta) => {
      if(type == "display"){
        let date = new Date(data * 1000)

        // Return formatted date "3/23/2021 10:52:28 AM"
        return isNaN(data) ? 'Invalid Date' : `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`
      }
      else{
        return data;
      }
    }}, // modified_at
    { name: 'owner', data: 'owner', visible: getShowOwnerMode() }, // owner
    { name: 'mode', data: 'mode', visible: getShowOwnerMode(), render: (data, type, row, meta) => {

      // mode after base conversion is a string such as "100755"
      let mode = data.toString(8)

      // only care about the last 3 bits (755)
      let chmodDisplay = mode.substring(mode.length - 3)

      return chmodDisplay
    }} // mode
  ]
});

// prepend show dotfiles checkbox to search box
$('#directory-contents_filter').prepend(`<label style="margin-right: 20px" for="show-dotfiles"><input type="checkbox" id="show-dotfiles" ${ getShowDotFiles() ? 'checked' : ''}> 显示隐藏</label>`)
$('#directory-contents_filter').prepend(`<label style="margin-right: 14px" for="show-owner-mode"><input type="checkbox" id="show-owner-mode" ${ getShowOwnerMode() ? 'checked' : ''}> 显示属性</label>`)

table.on('draw.dtSelect.dt select.dtSelect.dt deselect.dtSelect.dt info.dt', function () {
  update_datatables_status(table);
});

<% unless alert %>
// initial data load
reloadTable();
<% end %>

$('#show-dotfiles').on('change', () => {
  let visible = $('#show-dotfiles').is(':checked');

  setShowDotFiles(visible);
  updateDotFileVisibility();
});

$('#show-owner-mode').on('change', () => {
  let visible = $('#show-owner-mode').is(':checked');

  setShowOwnerMode(visible);
  updateShowOwnerModeVisibility();
});

$('#path-breadcrumbs').on('click', '#goto-btn', function(){
  Swal.fire({
    title: '修改目录',
    input: 'text',
    inputLabel: '路径',
    inputValue: history.state.currentDirectory,
    inputAttributes: {
      spellcheck: 'false',
    },
    showCancelButton: true,
    inputValidator: (value) => {
      if (! value || ! value.startsWith('/')) {
        // TODO: validate filenames against listing
        return 'Provide an absolute pathname'
      }
    }
  })
  .then((result) => result.isConfirmed ? Promise.resolve(result.value) : Promise.reject('cancelled'))
  .then((pathname) => goto('<%= files_path('/') %>' + pathname))
});

$('#new-file-btn').on("click", () => {
  Swal.fire({
    title: '创建文件',
    input: 'text',
    inputLabel: '文件名称',
    showCancelButton: true,
    inputValidator: (value) => {
      if (! value ) {
        // TODO: validate filenames against listing
        return 'Provide a non-empty filename.'
      }
      else if (value.includes("/")) {
        // TODO: validate filenames against listing
        return 'Illegal character (/) not allowed in filename.'
      }
    }
  })
  .then((result) => result.isConfirmed ? Promise.resolve(result.value) : Promise.reject('cancelled'))
  .then((filename) => newFile(filename));
});

$('#new-dir-btn').on("click", () => {
  Swal.fire({
    title: '创建目录',
    input: 'text',
    inputLabel: '目录名称',
    inputAttributes: {
      spellcheck: 'false',
    },
    showCancelButton: true,
    inputValidator: (value) => {
      if (! value || value.includes("/")) {
        // TODO: validate filenames against listing
        return 'Provide a directory name that does not have / in it'
      }
    },
  })
  .then((result) => result.isConfirmed ? Promise.resolve(result.value) : Promise.reject('cancelled'))
  .then((filename) => newDirectory(filename));
});

function downloadDirectory(file) {
  let filename = $($.parseHTML(file.name)).text(),
      canDownloadReq = `${history.state.currentDirectoryUrl}/${encodeURI(filename)}?can_download=${Date.now().toString()}`

  loading('preparing to download directory: ' + file.name)

  fetch(canDownloadReq, {
      method: 'GET',
      headers: {
        'X-CSRF-Token': csrf_token,
        'Accept': 'application/json'
      }
    })
    .then(response => dataFromJsonResponse(response))
    .then(data => {
      if (data.can_download) {
        doneLoading();
        downloadFile(file)
      } else {
        Swal.fire('<%= t('dashboard.files_directory_download_error_modal_title') %>', data.error_message, 'error')
      }
    })
    .catch(e => {
      Swal.fire('<%= t('dashboard.files_directory_download_error_modal_title') %>', e.message, 'error')
    })
}

function downloadFile(file) {
  // creating the temporary iframe is exactly what the CloudCmd does
  // so this just repeats the status quo

  let filename = $($.parseHTML(file.name)).text(),
      downloadUrl = `${history.state.currentDirectoryUrl}/${encodeURI(filename)}?download=${Date.now().toString()}`,
      iframe = document.createElement('iframe'),
      TIME = 30 * 1000;

  iframe.setAttribute('class', 'd-none');
  iframe.setAttribute('src', downloadUrl);

  document.body.appendChild(iframe);

  setTimeout(function() {
    document.body.removeChild(iframe);
  }, TIME);
}

$('#download-btn').on("click", () => {
  let selection = table.rows({ selected: true }).data();
  if(selection.length == 0) {
    Swal.fire('Select a file, files, or directory to download', 'You have selected none.', 'error');
  }
  selection.toArray().forEach( (f) => {
    if(f.type == 'd') {
      downloadDirectory(f)
    }
    else if(f.type == 'f') {
      downloadFile(f)
    }
  })
});

function getEmptyDirs(entry){
  return new Promise((resolve) => {
    if(entry.isFile){
      resolve([]);
    }
    else{
      // getFilesAndDirectoriesFromDirectory has no return value, so turn this into a promise
      getFilesAndDirectoriesFromDirectory(entry.createReader(), [], function(error){ console.error(error)}, {
        onSuccess: (entries) => {
          if(entries.length == 0){
            // this is an empty directory
            resolve([entry]);
          }
          else{
            Promise.all(entries.map(e => getEmptyDirs(e))).then((dirs) => resolve(_.flattenDeep(dirs)));
          }
        }
      })
    }
  });
}

// https://github.com/transloadit/uppy/blob/7ce58beeb620df3df0640cb369f5d71e3d3f751f/packages/%40uppy/utils/src/getDroppedFiles/utils/webkitGetAsEntryApi/getFilesAndDirectoriesFromDirectory.js
/**
 * Recursive function, calls the original callback() when the directory is entirely parsed.
 *
 * @param {FileSystemDirectoryReader} directoryReader
 * @param {Array} oldEntries
 * @param {Function} logDropError
 * @param {Function} callback - called with ([ all files and directories in that directoryReader ])
 */
function getFilesAndDirectoriesFromDirectory (directoryReader, oldEntries, logDropError, { onSuccess }) {
  directoryReader.readEntries(
    (entries) => {
      const newEntries = [...oldEntries, ...entries]
      // According to the FileSystem API spec, getFilesAndDirectoriesFromDirectory() must be called until it calls the onSuccess with an empty array.
      if (entries.length) {
        setTimeout(() => {
          getFilesAndDirectoriesFromDirectory(directoryReader, newEntries, logDropError, { onSuccess })
        }, 0)
      // Done iterating this particular directory
      } else {
        onSuccess(newEntries)
      }
    },
    // Make sure we resolve on error anyway, it's fine if only one directory couldn't be parsed!
    (error) => {
      logDropError(error)
      onSuccess(oldEntries)
    }
  )
}

(function(){
  class EmptyDirCreator extends BasePlugin {
    constructor (uppy, opts){
      super(uppy, opts)
      this.id = this.opts.id || 'EmptyDirUploaderCatcher';
      this.type = 'acquirer';

      this.empty_dirs = [];
      this.last_entries = [];

      this.handleRootDrop = this.handleRootDrop.bind(this);
      this.createEmptyDirs = this.createEmptyDirs.bind(this);

      this.uppy = uppy;
    }

    handleRootDrop (e) {
      // from https://github.com/transloadit/uppy/blob/7ce58beeb620df3df0640cb369f5d71e3d3f751f/packages/%40uppy/utils/src/getDroppedFiles/index.js
      if (e.dataTransfer.items && e.dataTransfer.items[0] && 'webkitGetAsEntry' in e.dataTransfer.items[0]) {
        // toArray https://github.com/transloadit/uppy/blob/7ce58beeb620df3df0640cb369f5d71e3d3f751f/packages/%40uppy/utils/src/toArray.js#L4
        let items = Array.prototype.slice.call(e.dataTransfer.items || [], 0);
        let entries = items.map(i => i.webkitGetAsEntry()).filter(i => i);

        return Promise.all(entries.map(i => getEmptyDirs(i))).then((dirs) => {
          this.empty_dirs = this.empty_dirs.concat(_.flattenDeep(dirs));

          console.log(this.empty_dirs);
        });
      }
      //else we don't have access to directory information
    }

    createEmptyDirs (ids) {
      if(! this.uppy.getState().error){ // avoid creating empty dirs if error occurred during upload

        //TODO: error checking and reporting
        return Promise.all(this.empty_dirs.map((d) => {
          // "fullPath" should actually be the path relative to the current directory
          let filename = _.trimStart(d.fullPath, '/');

          return fetch(`${history.state.currentDirectoryUrl}/${encodeURI(filename)}?dir=true`, {method: 'put', headers: { 'X-CSRF-Token': csrf_token }})
          //TODO: parse json response verify if there was an error creating directory and handle error

        })).then(() => this.empty_dirs = []);
      }
    }

    install () {
      this.uppy.addPostProcessor(this.createEmptyDirs);
    }

    uninstall () {
      this.uppy.removePostProcessor(this.createEmptyDirs);
    }
  }

  function closeAndResetUppyModal(uppy){
    uppy.getPlugin('Dashboard').closeModal();
    uppy.reset();
  }


  window.uppy = new Uppy({
    restrictions: {
      maxFileSize: <%= Configuration.file_upload_max %>,
    }
  });
  uppy.use(EmptyDirCreator);
  uppy.use(Dashboard, {
    trigger: '#upload-btn',
    fileManagerSelectionType: 'both',
    disableThumbnailGenerator: true,
    showLinkToFileUploadResult: false,
    closeModalOnClickOutside: true,
    closeAfterFinish: true,
    allowMultipleUploads: false,
    onRequestCloseModal: () => closeAndResetUppyModal(uppy),
    note: 'Empty directories will be included in the upload only when a directory upload is initiated via drag and drop. This is because the File and Directory Entries API is available only on a drop event, not during an input change event.'
  });
  uppy.use(XHRUpload, {
    endpoint: '<%= files_upload_path %>',
    withCredentials: true,
    fieldName: 'file',
    limit: 1,
    headers: { 'X-CSRF-Token': csrf_token },
    timeout: 128 * 1000,
  });

  uppy.on('file-added', (file) => {
    uppy.setFileMeta(file.id, { parent: history.state.currentDirectory });
    if(file.meta.relativePath == null && file.data.webkitRelativePath){
      uppy.setFileMeta(file.id, { relativePath: file.data.webkitRelativePath });
    }
  });

  uppy.on('complete', (result) => {
    if(result.successful.length > 0){
      reloadTable();
    }
  });

  // https://stackoverflow.com/questions/6756583/prevent-browser-from-loading-a-drag-and-dropped-file
  window.addEventListener("dragover",function(e){
    e = e || event;
    e.preventDefault();
  },false);
  window.addEventListener("drop",function(e){
    e = e || event;
    e.preventDefault();
  },false);

  $('#directory-contents').on('drop', function(e){
    this.classList.remove('dragover');
    console.log('File(s) dropped');
    // Prevent default behavior (Prevent file from being opened)

    // pass drop event to uppy dashboard
    uppy.getPlugin('Dashboard').openModal().then(() => uppy.getPlugin('Dashboard').handleDrop(e.originalEvent))
  });

  $('#directory-contents').on('dragover', function(e){
    this.classList.add('dragover');

    // Prevent default behavior (Prevent file from being opened)
    e.preventDefault();

    // specifies what feedback will be shown to the user by setting the dropEffect attribute of the DataTransfer associated with the event
    // too bad we can't show an indicator (no dragstart/end when dragging from OS to browser)
    e.originalEvent.dataTransfer.dropEffect = 'copy';
  });

  $('#directory-contents').on('dragleave', function(e){
    this.classList.remove('dragover');
  });

})();

window.onpopstate = function(event){
  // FIXME: handle edge case if state ! exist
  setTimeout(() => {
    goto(event.state.currentDirectoryUrl, false);
  }, 0);
};

// borrowed from Turbolinks
// event: MouseEvent
function clickEventIsSignificant(event) {
  return !(
    // (event.target && (event.target as any).isContentEditable)
       event.defaultPrevented
    || event.which > 1
    || event.altKey
    || event.ctrlKey
    || event.metaKey
    || event.shiftKey
  )
}

// this would be perfect for stimulus FYI
$('#directory-contents tbody').on('click', '.view-file', function(e){
  e.preventDefault();

  window.open(this.href, 'ViewFile', "location=yes,resizable=yes,scrollbars=yes,status=yes");
});

$('#directory-contents tbody').on('click', '.delete-file', function(e){
  e.preventDefault();

  let row = table.row(this.dataset.rowIndex).data();
  deleteFiles([row.name]);
});

$('#directory-contents tbody').on('click', '.download-file', function(e){
  e.preventDefault();

  let file = table.row(this.dataset.rowIndex).data();

  if(file.type == 'd') {
    downloadDirectory(file)
  }
  else {
    downloadFile(file)
  }
});

$('#directory-contents tbody').on('click', '.rename-file', function(e){
  e.preventDefault();

  let row = table.row(this.dataset.rowIndex).data();

  // if there was some other attribute that just had the name...
  let filename = $($.parseHTML(row.name)).text();

  Swal.fire({
    title: 'Rename',
    input: 'text',
    inputLabel: 'Filename',
    inputValue: filename,
    inputAttributes: {
      spellcheck: 'false',
    },
    showCancelButton: true,
    inputValidator: (value) => {
      if (! value) {
        // TODO: validate filenames against listing
        return 'Provide a filename to rename this to';
      }
      else if (value.includes('/') || value.includes('..')){
       return 'Filename cannot include / or ..';
      }
    }
  })
  .then((result) => result.isConfirmed ? Promise.resolve(result.value) : Promise.reject('cancelled'))
  .then((new_filename) => renameFile(filename, new_filename))
});

$('#directory-contents tbody, #path-breadcrumbs, #favorites').on('click', 'a.d', function(){
  if(clickEventIsSignificant(event)){
    event.preventDefault();
    event.cancelBubble = true;
    if(event.stopPropagation) event.stopPropagation();

    goto(this.getAttribute("href"));
  }
});

$('#directory-contents tbody').on('dblclick', 'tr td:not(:first-child)', function(){
    // handle doubleclick
    let a = this.parentElement.querySelector('a');
    if(a.classList.contains('d')) goto(a.getAttribute("href"));
});

function clearClipboard(){
   localStorage.removeItem('filesClipboard');
}

function updateClipboardFromSelection(){
  let selection = table.rows({selected: true}).data();
  if(selection.length == 0){
    clearClipboard();
  }
  else {
    let clipboardData = {
      from: history.state.currentDirectory,
      files: selection.toArray().map((f) => {
          return { directory: f.type == 'd', name: f.name };
      })
    };

    localStorage.setItem('filesClipboard', JSON.stringify(clipboardData));
  }
}

function transferFiles(files, action, summary){
  loading(_.startCase(summary));

  return fetch('<%= transfers_path(format: "json") %>', {
    method: 'post',
    body: JSON.stringify({
      command: action,
      files: files
    }),
    headers: { 'X-CSRF-Token': csrf_token }
  })
  .then(response => dataFromJsonResponse(response))
  .then((data) => {

    if(! data.completed){
      // was async, gotta report on progress and start polling
      reportTransfer(data);
    }
    else {
      if(data.target_dir == history.state.currentDirectory){
        reloadTable();
      }
    }

    if(action == 'mv' || action == 'cp'){
      clearClipboard();
      updateViewForClipboard();
    }
  })
  .then(() => doneLoading())
  .catch(e => alertError('Error occurred when attempting to ' + summary, e.message))
}

const reportTransferTemplate = (function(){
  let template_str  = $('#transfer-template').html();
  return Handlebars.compile(template_str);
})();

function findAndUpdateTransferStatus(data){
  let id = `#${data.id}`;

  if($(id).length){
    $(id).replaceWith(reportTransferTemplate(data));
  }
  else{
    $('.transfers-status').append(reportTransferTemplate(data));
  }
}

function fadeOutTransferStatus(data){
  let id = `#${data.id}`;
  $(id).fadeOut(4000);
}


function reportTransfer(data){
  // 1. add the transfer label
  findAndUpdateTransferStatus(data);

  // 2. poll for the updates
  var poll = function(){
   $.getJSON(data.show_json_url, function(newdata){
     findAndUpdateTransferStatus(newdata);

     if(newdata.completed){
       if(! newdata.error_message){
         if(newdata.target_dir == history.state.currentDirectory){
           reloadTable();
         }

         // 3. fade out after 5 seconds
         fadeOutTransferStatus(newdata)
       }
     }
     else{
       // not completed yet, so poll again
       setTimeout(poll, 1000);
     }
   }).fail(function(){
     setTimeout(poll, 1000);
   });
  }

  poll();
}

function renameFile(filename, new_filename){
  let files = {};
  files[`${history.state.currentDirectory}/${filename}`] = `${history.state.currentDirectory}/${new_filename}`;
  transferFiles(files, "mv", "rename file")
}

function moveFiles(files, summary = "move files"){
  transferFiles(files, "mv", "move files")
}

function copyFiles(files){
  transferFiles(files, "cp", "copy files")
}

function removeFiles(files){
  transferFiles(files, "rm", "remove files")
}

function updateViewForClipboard(){
  let clipboard = JSON.parse(localStorage.getItem('filesClipboard') || '{}'),
      template_str  = $('#clipboard-template').html(),
      template = Handlebars.compile(template_str);

  $('#clipboard').html(template(clipboard));

  $('#clipboard-clear').on("click", () => {
      clearClipboard();
      updateViewForClipboard();
  });


  $('#clipboard-copy-to-dir').on("click", () => {
    let clipboard = JSON.parse(localStorage.getItem('filesClipboard') || 'null');

    if(clipboard){
      clipboard.to = history.state.currentDirectory;

      if(clipboard.from == clipboard.to){
        console.error('clipboard from and to are identical')

        // TODO: we want to support this use case
        // copy and paste as a new filename
        // but lots of edge cases
        // (overwrite or rename duplicates)
        // _copy
        // _copy_2
        // _copy_3
        // _copy_4
      }
      else{
        // [{"/from/file/path":"/to/file/path" }]
        let files = {};
        clipboard.files.forEach((f) => {
          files[`${clipboard.from}/${f.name}`] = `${history.state.currentDirectory}/${f.name}`
        });

        copyFiles(files, csrf_token);
      }
    }
    else{
      console.error('files clipboard is empty');
    }
  });

  $('#clipboard-move-to-dir').on("click", () => {
    let clipboard = JSON.parse(localStorage.getItem('filesClipboard') || 'null');

    if(clipboard){
      clipboard.to = history.state.currentDirectory;

      if(clipboard.from == clipboard.to){
        console.error('clipboard from and to are identical')
        // TODO:
      }
      else{
        let files = {};
        clipboard.files.forEach((f) => {
          files[`${clipboard.from}/${f.name}`] = `${history.state.currentDirectory}/${f.name}`
        });

        moveFiles(files, csrf_token);
      }
    }
    else{
      console.error('files clipboard is empty');
    }
  });
}

$('#copy-move-btn').on("click", () => {
    updateClipboardFromSelection();
    updateViewForClipboard();
});

function deleteFiles(files){
  if(! files.length > 0){
    return;
  }

  Swal.fire({
    title: files.length == 1 ? `Delete ${files[0]}?` : `Delete ${files.length} selected files?`,
    text: 'Are you sure you want to delete the files: ' + files.join(', '),
    showCancelButton: true,
  })
  .then((result) => {
    if(result.isConfirmed){
      loading('Deleting files...');
      removeFiles(files.map(f => [history.state.currentDirectory, f].join('/')), csrf_token);
    }
  })
}

$('#delete-btn').on("click", () => {
  let files = table.rows({selected: true}).data().toArray().map((f) => f.name);
  deleteFiles(files);
});

// TODO: move all functionality out of click handlers to functions
// TODO: disable copy/move buttons if no local storage

//FIXME: so need to handle updateViewForClipboard based on EVENTS emitted by local storage modifications
updateViewForClipboard();
window.addEventListener('storage', () => {
  updateViewForClipboard();
});

// if only 1 selected item, do not allow to de-select
table.on('user-select', function ( e, dt, type, cell, originalEvent  ) {
    var selected_rows = dt.rows( { selected: true  }  );

    if(originalEvent.target.closest('.actions-btn-group')){
      // dont do user select event when opening or working with actions btn dropdown
      e.preventDefault();
    }
    else if(selected_rows.count() == 1 && cell.index().row == selected_rows.indexes()[0] ){
      // dont do user select because already selected
      e.preventDefault();
    }
    else{
      // row need to find the checkbox to give it the focus
      cell.node().closest('tr').querySelector('input[type=checkbox]').focus();
    }
});

table.on( 'deselect', function ( e, dt, type, indexes ) {
  dt.rows(indexes).nodes().toArray().forEach(e => $(e).find('input[type=checkbox]').prop('checked', false));
});

table.on( 'select', function ( e, dt, type, indexes ) {
  dt.rows(indexes).nodes().toArray().forEach(e => $(e).find('input[type=checkbox]').prop('checked', true));
});

$('#directory-contents tbody').on('click', 'tr td:first-child input[type=checkbox]', function(){
  // input checkbox checked or not

  if($(this).is(':checked')){
    // select row
    table.row(this.closest('tr')).select();
  }
  else{
    // deselect row
    table.row(this.closest('tr')).deselect();
  }

  this.focus();
});

$('#directory-contents tbody').on('keydown', 'input, a', function(e){
  if(e.key == "ArrowDown"){
    e.preventDefault();

    // let tr = this.closest('tr').nextSibling;
    let tr = $(this.closest('tr')).next('tr').get(0);
    if(tr){
      tr.querySelector('input[type=checkbox]').focus();

      // deselect if not holding shift key to work
      // like native file browsers
      if(! e.shiftKey){
        table.rows().deselect();
      }

      // select if moving down
      table.row(tr).select();
    }
  }
  else if(e.key == "ArrowUp"){
    e.preventDefault();

    let tr = $(this.closest('tr')).prev('tr').get(0);
    if(tr){
      tr.querySelector('input[type=checkbox]').focus();

      // deselect if not holding shift key to work
      // like native file browsers
      if(! e.shiftKey){
        table.rows().deselect();
      }

      // select if moving up
      table.row(tr).select();
    }
  }
});

</script>
