<% if file_counter == 0 %>
  <li class="breadcrumb-item">
    <a href="<%= files_path(full_path.parent) %>" style="margin-right: 20px;" class="d btn btn-outline-dark btn-sm" title="Go up directory">
      <span class="fa fa-arrow-up"></span></a>
  </li>
<% end %>

<% if file_counter == (file_count - 1) %>
  <li class="breadcrumb-item">
    <%= path_segment_with_slash(file.basename.to_s, file_counter, file_count) %>
  </li>
  <li class="breadcrumb-item ml-3">
    <button id="goto-btn" type="button" class="btn btn-outline-dark btn-sm"><i class="fas fa-edit" aria-hidden="true"></i> 修改目录</button>
  </li>
  <li class="breadcrumb-item ml-auto">
    <button id="copy-path" class="btn btn-outline-dark btn-sm " data-clipboard-text="<%= file.to_s %>"><span class="fa fa-clipboard"></span> 拷贝目录</button>
  </li>
<% else %>
  <li class="breadcrumb-item">
    <%= link_to path_segment_with_slash(file.basename.to_s, file_counter, file_count), files_path(file), class: (file_counter == (file_count - 1) ? 'd active' : 'd')  %>
  </li>
<% end %>
