require 'net-ldap'

class CustomLdap
  class << self
    # 查询用户
    def query_user_list(username = nil)
      ldap = Net::LDAP.new(host: 'mgt01', port: 389)

      if username.is_a?(String)
        filter = Net::LDAP::Filter.eq('uid', username)
        ldap_entry = ldap.search(base: 'dc=hpc,dc=com', filter: filter)
      elsif username.is_a?(Array)
        final_query = ""
        username.each do |uid|
          final_query << "(uid=#{uid})"
        end
        filter = Net::LDAP::Filter.construct("(|#{final_query})")
        ldap_entry = ldap.search(base: 'dc=hpc,dc=com', filter: filter)
      else
        ldap_entry = ldap.search(base: 'dc=hpc,dc=com')
      end

      ldap_to_json(ldap_entry)
    end

    def query_account(username)
      begin
        output = `sacctmgr show user #{username}`
        lines = output.split("\n")
        # Extract the header and rows
        header = lines[0].split
        rows = lines[2].split

        # Create a hash mapping header to row values
        table = header.zip(rows).to_h

        # Access the value associated with 'Def Acct' for 'test1'
        result = table['Def']
      rescue => e
        ""
      end
    end

    def query_raw(start_time, end_time, dtype, username=nil)
      if dtype == "elapsedRAW,alloctres"
        if username.blank?
          output = `sacct -S #{start_time} -E #{end_time} --format=#{dtype}   --allusers --parsable2 --noheader |grep gpu`
        else
          output = `sacct -A #{username} -S #{start_time} -E #{end_time} --format=#{dtype}   --allusers --parsable2 --noheader |grep gpu`
        end
      else
        if username.blank?
          output = `sacct -S #{start_time} -E #{end_time} --format=#{dtype}   --allusers --parsable2 --noheader`
        else
          output = `sacct -A #{username} -S #{start_time} -E #{end_time} --format=#{dtype}   --allusers --parsable2 --noheader`
        end
      end
      lines = output.split("\n")
      (lines.map(&:to_i).sum / 3600.0).round(2)
    end

    # 添加用户
    def add_user(username, password, uid_number, gid_number, home_directory, remark=nil)
      begin
        result = `useradd.ldap -w Password@_ -d #{home_directory} -p #{password} -s /bin/bash -U -c dc=hpc,dc=com -o ou=People #{username}`
        UserInfo.find_or_create_by(username: username).update(remark: remark) if remark.present?
        true
      rescue => e
        false
      end
    end

    # 修改用户
    def change_user(username, new_password=nil, remark=nil)
      tmp_info = `ldapsearch -x -H ldap://mgt01:389 -b dc=hpc,dc=com -D "cn=Manager,dc=hpc,dc=com" -w Password@_  |grep -w #{username}  |grep dn  | tail -n 1  | awk -F"ou" 'sub($1,"")'`
      if new_password.present?
        `ldappasswd -x -h 192.168.0.110 -p 389 -D "cn=Manager,dc=hpc,dc=com" -w Password@_ "cn=#{username},#{tmp_info}" -s #{new_password}`
      end
      UserInfo.find_or_create_by(username: username).update(remark: remark) if remark.present?
    end

    def ldap_to_json(ldap_entrys)
      final_json = []
      ldap_entrys.each do |ldap_entry|
        result = {}

        ldap_entry.each do |attribute, values|
          if values.size > 1
            result[attribute.to_sym] = values
          else
            result[attribute.to_sym] = values.first
          end
        end

        final_json << result.to_json if !result.to_json["sn"].nil?
      end
      final_json
    end

    # 删除用户
    def delete_user(username)
      `userdel.ldap -w Password@_ #{username}`
    end

    # 冻结用户
    def freeze_user(username)
      `sudo sacctmgr -i del acc name=#{username} cluster=cluster`
      `sudo sacctmgr -i del user name=#{username} acc=#{username} cluster=cluster`
    end

    # 添加权限
    def auth_user(username, account_name)
      #`sudo sacctmgr -i add acc name=#{username} cluster=cluster`
      `sudo sacctmgr -i add user name=#{username} acc=#{account_name} cluster=cluster`
    end
  end
end
