class MachineTimeController < ApplicationController
  before_action :set_layout_container_class

  def index
    @current_tab = params[:tab] || 'cpu'
    @page = params[:page] || 1

    # 获取机时记录数据（根据tab类型）
    @machine_time_records = get_machine_time_records(@current_tab)

    # 分页处理
    @machine_time_records = Kaminari.paginate_array(@machine_time_records).page(@page).per(10)
  end

  private

  def set_layout_container_class
    @layout_container_class = 'container-md'
  end

  # 获取机时记录数据（模拟数据，后续可替换为真实的Linux命令获取）
  def get_machine_time_records(tab_type)
    case tab_type
    when 'cpu'
      get_cpu_records
    when 'gpu'
      get_gpu_records
    when 'ram'
      get_ram_records
    else
      get_cpu_records
    end
  end

  # CPU使用记录模拟数据
  def get_cpu_records
    [
      {
        task_name: '任务名称1',
        usage_time: '2核',
        start_time: '2025.04.12 12:24:56',
        remaining_time: '1:12:58',
        remaining_machine_time: '0:58:12'
      },
      {
        task_name: '任务名称2',
        usage_time: '4核',
        start_time: '2025.04.12 11:30:22',
        remaining_time: '2:45:30',
        remaining_machine_time: '1:25:45'
      },
      {
        task_name: '任务名称3',
        usage_time: '8核',
        start_time: '2025.04.12 10:15:10',
        remaining_time: '0:30:15',
        remaining_machine_time: '0:15:30'
      },
      {
        task_name: '任务名称4',
        usage_time: '2核',
        start_time: '2025.04.12 09:45:33',
        remaining_time: '3:20:45',
        remaining_machine_time: '2:10:20'
      },
      {
        task_name: '任务名称5',
        usage_time: '6核',
        start_time: '2025.04.12 08:20:15',
        remaining_time: '1:55:12',
        remaining_machine_time: '0:45:30'
      },
      {
        task_name: '任务名称6',
        usage_time: '2核',
        start_time: '2025.04.11 16:30:45',
        remaining_time: '4:15:20',
        remaining_machine_time: '3:30:15'
      },
      {
        task_name: '任务名称7',
        usage_time: '12核',
        start_time: '2025.04.11 14:20:30',
        remaining_time: '0:45:10',
        remaining_machine_time: '0:20:05'
      },
      {
        task_name: '任务名称8',
        usage_time: '4核',
        start_time: '2025.04.11 12:15:20',
        remaining_time: '2:30:45',
        remaining_machine_time: '1:15:30'
      },
      {
        task_name: '任务名称9',
        usage_time: '8核',
        start_time: '2025.04.11 10:45:15',
        remaining_time: '1:20:30',
        remaining_machine_time: '0:40:15'
      },
      {
        task_name: '任务名称10',
        usage_time: '2核',
        start_time: '2025.04.11 09:30:10',
        remaining_time: '5:10:25',
        remaining_machine_time: '4:20:15'
      },
      {
        task_name: '任务名称11',
        usage_time: '16核',
        start_time: '2025.04.10 18:20:45',
        remaining_time: '0:25:15',
        remaining_machine_time: '0:10:30'
      },
      {
        task_name: '任务名称12',
        usage_time: '6核',
        start_time: '2025.04.10 15:45:30',
        remaining_time: '3:45:20',
        remaining_machine_time: '2:30:10'
      }
    ]
  end

  # GPU使用记录模拟数据
  def get_gpu_records
    [
      {
        task_name: 'GPU任务1',
        usage_time: '1GPU',
        start_time: '2025.04.12 12:24:56',
        remaining_time: '2:12:58',
        remaining_machine_time: '1:58:12'
      },
      {
        task_name: 'GPU任务2',
        usage_time: '2GPU',
        start_time: '2025.04.12 11:30:22',
        remaining_time: '1:45:30',
        remaining_machine_time: '0:55:45'
      },
      {
        task_name: 'GPU任务3',
        usage_time: '4GPU',
        start_time: '2025.04.12 10:15:10',
        remaining_time: '3:30:15',
        remaining_machine_time: '2:45:30'
      },
      {
        task_name: 'GPU任务4',
        usage_time: '1GPU',
        start_time: '2025.04.12 09:45:33',
        remaining_time: '0:50:45',
        remaining_machine_time: '0:30:20'
      },
      {
        task_name: 'GPU任务5',
        usage_time: '8GPU',
        start_time: '2025.04.12 08:20:15',
        remaining_time: '4:25:12',
        remaining_machine_time: '3:15:30'
      }
    ]
  end

  # RAM使用记录模拟数据
  def get_ram_records
    [
      {
        task_name: 'RAM任务1',
        usage_time: '32GB',
        start_time: '2025.04.12 12:24:56',
        remaining_time: '1:42:58',
        remaining_machine_time: '1:18:12'
      },
      {
        task_name: 'RAM任务2',
        usage_time: '64GB',
        start_time: '2025.04.12 11:30:22',
        remaining_time: '2:15:30',
        remaining_machine_time: '1:45:45'
      },
      {
        task_name: 'RAM任务3',
        usage_time: '128GB',
        start_time: '2025.04.12 10:15:10',
        remaining_time: '0:45:15',
        remaining_machine_time: '0:25:30'
      },
      {
        task_name: 'RAM任务4',
        usage_time: '16GB',
        start_time: '2025.04.12 09:45:33',
        remaining_time: '3:10:45',
        remaining_machine_time: '2:40:20'
      },
      {
        task_name: 'RAM任务5',
        usage_time: '256GB',
        start_time: '2025.04.12 08:20:15',
        remaining_time: '5:35:12',
        remaining_machine_time: '4:55:30'
      }
    ]
  end

  # 未来可以添加真实的Linux命令获取方法
  # def get_real_cpu_usage
  #   # 使用系统命令获取CPU使用情况
  #   # 例如: `top -bn1 | grep "Cpu(s)"`
  # end

  # def get_real_memory_usage
  #   # 使用系统命令获取内存使用情况
  #   # 例如: `free -h`
  # end

  # def get_real_gpu_usage
  #   # 使用nvidia-smi获取GPU使用情况
  #   # 例如: `nvidia-smi --query-gpu=utilization.gpu --format=csv,noheader,nounits`
  # end
end
